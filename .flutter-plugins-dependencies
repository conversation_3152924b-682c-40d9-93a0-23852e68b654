{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-3.11.2/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-1.24.0/", "native_build": true, "dependencies": []}], "android": [{"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-3.11.2/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-1.24.0/", "native_build": true, "dependencies": []}], "macos": [{"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-3.11.2/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-1.24.0/", "native_build": true, "dependencies": []}], "linux": [], "windows": [], "web": [{"name": "cloud_firestore_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_web-2.8.10/", "dependencies": ["firebase_core_web"]}, {"name": "firebase_auth_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_web-4.6.1/", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-1.7.3/", "dependencies": []}]}, "dependencyGraph": [{"name": "cloud_firestore_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_auth", "dependencies": ["firebase_auth_web", "firebase_core"]}, {"name": "firebase_auth_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}], "date_created": "2025-05-27 22:55:07.039298", "version": "3.27.1", "swift_package_manager_enabled": false}